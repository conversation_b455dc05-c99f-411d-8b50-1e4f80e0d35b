/**
 * Component-Specific Theming System for Zenith Flow
 * نظام التصميم المخصص للمكونات في تطبيق Zenith Flow
 * 
 * Provides specialized theming for different component types with:
 * - Consistent spacing scales
 * - Typography hierarchies
 * - Color schemes optimized for each component
 * - Responsive design tokens
 * - Accessibility-first approach
 */

import { colors } from './colors';

// Spacing scale based on 8px grid system
export const spacing = {
  xs: '0.25rem',    // 4px
  sm: '0.5rem',     // 8px
  md: '0.75rem',    // 12px
  lg: '1rem',       // 16px
  xl: '1.5rem',     // 24px
  '2xl': '2rem',    // 32px
  '3xl': '3rem',    // 48px
  '4xl': '4rem',    // 64px
  '5xl': '6rem',    // 96px
  '6xl': '8rem',    // 128px
} as const;

// Typography scale
export const typography = {
  fontSize: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
    '6xl': '3.75rem', // 60px
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  lineHeight: {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
  },
  letterSpacing: {
    tight: '-0.025em',
    normal: '0',
    wide: '0.025em',
  }
} as const;

// Border radius scale
export const borderRadius = {
  none: '0',
  sm: '0.125rem',   // 2px
  md: '0.375rem',   // 6px
  lg: '0.5rem',     // 8px
  xl: '0.75rem',    // 12px
  '2xl': '1rem',    // 16px
  '3xl': '1.5rem',  // 24px
  full: '9999px',
} as const;

// Shadow scale
export const shadows = {
  sm: '0 1px 2px 0 oklch(var(--foreground) / 0.05)',
  md: '0 4px 6px -1px oklch(var(--foreground) / 0.1), 0 2px 4px -1px oklch(var(--foreground) / 0.06)',
  lg: '0 10px 15px -3px oklch(var(--foreground) / 0.1), 0 4px 6px -2px oklch(var(--foreground) / 0.05)',
  xl: '0 20px 25px -5px oklch(var(--foreground) / 0.1), 0 10px 10px -5px oklch(var(--foreground) / 0.04)',
  '2xl': '0 25px 50px -12px oklch(var(--foreground) / 0.25)',
  inner: 'inset 0 2px 4px 0 oklch(var(--foreground) / 0.06)',
} as const;

// Component-specific themes using modern OKLCH system
export const componentThemes = {
  // Button component theming - OKLCH-based with dark mode support
  button: {
    base: {
      fontSize: typography.fontSize.sm,
      fontWeight: typography.fontWeight.medium,
      borderRadius: borderRadius.md,
      padding: `${spacing.md} ${spacing.lg}`,
      transition: 'all 0.2s ease-in-out',
      cursor: 'pointer',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      gap: spacing.sm,
    },
    variants: {
      primary: {
        background: 'oklch(var(--primary))',
        color: 'oklch(var(--primary-foreground))',
        border: 'transparent',
        hover: {
          background: 'oklch(var(--primary) / 0.9)',
          transform: 'translateY(-1px)',
          boxShadow: shadows.md,
        },
        active: {
          background: 'oklch(var(--primary) / 0.8)',
          transform: 'translateY(0)',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      },
      secondary: {
        background: 'oklch(var(--secondary))',
        color: 'oklch(var(--secondary-foreground))',
        border: '1px solid oklch(var(--border))',
        hover: {
          background: 'oklch(var(--secondary) / 0.8)',
          borderColor: 'oklch(var(--border))',
        },
        active: {
          background: 'oklch(var(--secondary) / 0.7)',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      },
      outline: {
        background: 'transparent',
        color: 'oklch(var(--foreground))',
        border: '1px solid oklch(var(--border))',
        hover: {
          background: 'oklch(var(--accent))',
          color: 'oklch(var(--accent-foreground))',
        },
        active: {
          background: 'oklch(var(--accent) / 0.8)',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      },
      ghost: {
        background: 'transparent',
        color: 'oklch(var(--foreground))',
        border: 'transparent',
        hover: {
          background: 'oklch(var(--accent))',
          color: 'oklch(var(--accent-foreground))',
        },
        active: {
          background: 'oklch(var(--accent) / 0.8)',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      },
      destructive: {
        background: 'oklch(var(--destructive))',
        color: 'oklch(var(--destructive-foreground))',
        border: 'transparent',
        hover: {
          background: 'oklch(var(--destructive) / 0.9)',
        },
        active: {
          background: 'oklch(var(--destructive) / 0.8)',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      }
    },
    sizes: {
      xs: {
        fontSize: typography.fontSize.xs,
        padding: `${spacing.xs} ${spacing.sm}`,
        borderRadius: borderRadius.sm,
      },
      sm: {
        fontSize: typography.fontSize.sm,
        padding: `${spacing.sm} ${spacing.md}`,
        borderRadius: borderRadius.md,
      },
      md: {
        fontSize: typography.fontSize.base,
        padding: `${spacing.md} ${spacing.lg}`,
        borderRadius: borderRadius.md,
      },
      lg: {
        fontSize: typography.fontSize.lg,
        padding: `${spacing.lg} ${spacing.xl}`,
        borderRadius: borderRadius.lg,
      }
    }
  },

  // Card component theming - OKLCH-based with dark mode support
  card: {
    base: {
      background: 'oklch(var(--card))',
      color: 'oklch(var(--card-foreground))',
      border: '1px solid oklch(var(--border))',
      borderRadius: borderRadius.lg,
      padding: spacing.xl,
      boxShadow: shadows.sm,
      transition: 'all 0.2s ease-in-out',
    },
    variants: {
      default: {
        background: 'oklch(var(--card))',
        border: '1px solid oklch(var(--border))',
      },
      elevated: {
        background: 'oklch(var(--card))',
        border: 'none',
        boxShadow: shadows.lg,
      },
      outlined: {
        background: 'transparent',
        border: '2px solid oklch(var(--border))',
        boxShadow: 'none',
      },
      interactive: {
        cursor: 'pointer',
        hover: {
          boxShadow: shadows.md,
          transform: 'translateY(-2px)',
          borderColor: 'oklch(var(--ring))',
        }
      },
      muted: {
        background: 'oklch(var(--muted))',
        color: 'oklch(var(--muted-foreground))',
        border: '1px solid oklch(var(--border))',
      }
    }
  },

  // Input component theming - OKLCH-based with dark mode support
  input: {
    base: {
      fontSize: typography.fontSize.base,
      padding: `${spacing.md} ${spacing.lg}`,
      borderRadius: borderRadius.md,
      border: '1px solid oklch(var(--border))',
      background: 'oklch(var(--background))',
      color: 'oklch(var(--foreground))',
      transition: 'all 0.2s ease-in-out',
      width: '100%',
    },
    states: {
      default: {
        border: '1px solid oklch(var(--border))',
        focus: {
          outline: 'none',
          borderColor: 'oklch(var(--ring))',
          boxShadow: '0 0 0 2px oklch(var(--ring) / 0.2)',
        },
        hover: {
          borderColor: 'oklch(var(--border) / 0.8)',
        }
      },
      error: {
        border: '1px solid oklch(var(--destructive))',
        focus: {
          borderColor: 'oklch(var(--destructive))',
          boxShadow: '0 0 0 2px oklch(var(--destructive) / 0.2)',
        }
      },
      disabled: {
        background: 'oklch(var(--muted))',
        color: 'oklch(var(--muted-foreground))',
        cursor: 'not-allowed',
        opacity: '0.6',
      },
      readonly: {
        background: 'oklch(var(--muted) / 0.5)',
        cursor: 'default',
      }
    }
  },

  // Badge component theming - OKLCH-based with dark mode support
  badge: {
    base: {
      fontSize: typography.fontSize.xs,
      fontWeight: typography.fontWeight.medium,
      padding: `${spacing.xs} ${spacing.sm}`,
      borderRadius: borderRadius.full,
      display: 'inline-flex',
      alignItems: 'center',
      gap: spacing.xs,
      transition: 'all 0.2s ease-in-out',
    },
    variants: {
      default: {
        background: 'oklch(var(--secondary))',
        color: 'oklch(var(--secondary-foreground))',
      },
      primary: {
        background: 'oklch(var(--primary))',
        color: 'oklch(var(--primary-foreground))',
      },
      secondary: {
        background: 'oklch(var(--secondary))',
        color: 'oklch(var(--secondary-foreground))',
      },
      destructive: {
        background: 'oklch(var(--destructive))',
        color: 'oklch(var(--destructive-foreground))',
      },
      outline: {
        background: 'transparent',
        color: 'oklch(var(--foreground))',
        border: '1px solid oklch(var(--border))',
      },
      muted: {
        background: 'oklch(var(--muted))',
        color: 'oklch(var(--muted-foreground))',
      }
    }
  },

  // Navigation component theming - OKLCH-based with dark mode support
  navigation: {
    base: {
      background: 'oklch(var(--card))',
      border: '1px solid oklch(var(--border))',
      padding: spacing.lg,
    },
    item: {
      base: {
        padding: `${spacing.md} ${spacing.lg}`,
        borderRadius: borderRadius.md,
        fontSize: typography.fontSize.sm,
        fontWeight: typography.fontWeight.medium,
        color: 'oklch(var(--muted-foreground))',
        transition: 'all 0.2s ease-in-out',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        gap: spacing.md,
      },
      states: {
        default: {
          color: 'oklch(var(--muted-foreground))',
          hover: {
            background: 'oklch(var(--accent))',
            color: 'oklch(var(--accent-foreground))',
          }
        },
        active: {
          background: 'oklch(var(--accent))',
          color: 'oklch(var(--accent-foreground))',
          borderLeft: '3px solid oklch(var(--primary))',
        },
        focus: {
          outline: 'none',
          boxShadow: '0 0 0 2px oklch(var(--ring))',
        }
      }
    }
  },

  // Modal/Dialog component theming - OKLCH-based with dark mode support
  modal: {
    overlay: {
      background: 'oklch(0 0 0 / 0.8)',
      backdropFilter: 'blur(4px)',
      transition: 'all 0.2s ease-in-out',
    },
    content: {
      background: 'oklch(var(--card))',
      color: 'oklch(var(--card-foreground))',
      border: '1px solid oklch(var(--border))',
      borderRadius: borderRadius.lg,
      boxShadow: shadows['2xl'],
      padding: spacing['2xl'],
      maxWidth: '32rem',
      width: '90vw',
    }
  },

  // Tooltip component theming - OKLCH-based with dark mode support
  tooltip: {
    base: {
      background: 'oklch(var(--popover))',
      color: 'oklch(var(--popover-foreground))',
      border: '1px solid oklch(var(--border))',
      borderRadius: borderRadius.md,
      padding: `${spacing.sm} ${spacing.md}`,
      fontSize: typography.fontSize.sm,
      boxShadow: shadows.md,
      maxWidth: '20rem',
    }
  }
} as const;

// Responsive breakpoints
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
} as const;

// Animation presets
export const animations = {
  duration: {
    fast: '150ms',
    normal: '200ms',
    slow: '300ms',
  },
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
  }
} as const;

// Export everything
export {
  spacing,
  typography,
  borderRadius,
  shadows,
  componentThemes,
  animations
};

// Default export
export default {
  spacing,
  typography,
  borderRadius,
  shadows,
  componentThemes,
  breakpoints,
  animations
};
