
import React from 'react';
import { Pa<PERSON>, Bell, Shield, Info, Globe, Type, Monitor } from 'lucide-react';
import { useLanguage } from '@/contexts/LanguageContext';
import LanguageToggle from '@/components/ui/LanguageToggle';
import ThemeToggle from '@/components/ui/ThemeToggle';
import { cn } from '@/lib/utils';

const Settings: React.FC = () => {
  const { t, isRTL } = useLanguage();

  const settingsGroups = [
    {
      title: t('settings.appearance'),
      icon: Palette,
      settings: [
        {
          label: t('settings.theme'),
          description: isRTL ? 'اختر بين الوضع الفاتح والداكن' : 'Choose between light and dark mode',
          component: <ThemeToggle variant="default" showLabel={true} />,
        },
        {
          label: t('settings.fontSize'),
          description: isRTL ? 'اضبط حجم النص المناسب لك' : 'Adjust text size to your preference',
          component: (
            <select className="px-3 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg text-gray-900 dark:text-white transition-colors duration-200">
              <option value="small">{isRTL ? 'صغير' : 'Small'}</option>
              <option value="medium">{isRTL ? 'متوسط' : 'Medium'}</option>
              <option value="large">{isRTL ? 'كبير' : 'Large'}</option>
            </select>
          ),
        },
      ],
    },
    {
      title: t('settings.language'),
      icon: Globe,
      settings: [
        {
          label: t('settings.language'),
          description: isRTL ? 'اختر لغة التطبيق المفضلة' : 'Choose your preferred app language',
          component: <LanguageToggle />,
        },
      ],
    },
    {
      title: t('settings.notifications'),
      icon: Bell,
      settings: [
        {
          label: isRTL ? 'إشعارات المهام' : 'Task Notifications',
          description: isRTL ? 'تلقي إشعارات عند اقتراب موعد المهام' : 'Receive notifications when tasks are due',
          component: (
            <button className="w-12 h-6 bg-zenith-gradient rounded-full relative transition-colors">
              <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 right-0.5 transition-transform"></div>
            </button>
          ),
        },
        {
          label: isRTL ? 'إشعارات البريد الإلكتروني' : 'Email Notifications',
          description: isRTL ? 'تلقي ملخص يومي بالبريد الإلكتروني' : 'Receive daily summary via email',
          component: (
            <button className="w-12 h-6 bg-muted rounded-full relative transition-colors">
              <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform"></div>
            </button>
          ),
        },
      ],
    },
    {
      title: t('settings.privacy'),
      icon: Shield,
      settings: [
        {
          label: isRTL ? 'مشاركة البيانات' : 'Data Sharing',
          description: isRTL ? 'السماح بمشاركة البيانات لتحسين الخدمة' : 'Allow data sharing to improve service',
          component: (
            <button className="w-12 h-6 bg-muted rounded-full relative transition-colors">
              <div className="w-5 h-5 bg-white rounded-full absolute top-0.5 left-0.5 transition-transform"></div>
            </button>
          ),
        },
        {
          label: isRTL ? 'المصادقة الثنائية' : 'Two-Factor Authentication',
          description: isRTL ? 'إضافة طبقة حماية إضافية لحسابك' : 'Add extra security layer to your account',
          component: (
            <button className="px-4 py-2 bg-zenith-gradient text-white rounded-lg hover:shadow-zenith transition-all duration-200">
              {isRTL ? 'تفعيل' : 'Enable'}
            </button>
          ),
        },
      ],
    },
    {
      title: t('settings.about'),
      icon: Info,
      settings: [
        {
          label: isRTL ? 'إصدار التطبيق' : 'App Version',
          description: 'Zenith Flow v1.0.0',
          component: (
            <span className="px-3 py-1 bg-zenith-sage-100 dark:bg-zenith-sage-900/30 text-zenith-sage-700 dark:text-zenith-sage-300 rounded-full text-sm font-medium">
              v1.0.0
            </span>
          ),
        },
        {
          label: isRTL ? 'التحديثات' : 'Updates',
          description: isRTL ? 'البحث عن تحديثات جديدة' : 'Check for new updates',
          component: (
            <button className="px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-900 dark:text-white rounded-lg transition-colors duration-200">
              {isRTL ? 'فحص التحديثات' : 'Check Updates'}
            </button>
          ),
        },
      ],
    },
  ];

  return (
    <div className="p-6 space-y-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className={cn(isRTL && "text-right")}>
        <h1 className="text-3xl font-bold">{t('settings.title')}</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">{t('settings.subtitle')}</p>
      </div>

      {/* Settings Groups */}
      <div className="space-y-8">
        {settingsGroups.map((group, groupIndex) => (
          <div key={groupIndex} className="space-y-4">
            <div className={cn(
              "flex items-center gap-3 pb-2 border-b border-gray-200 dark:border-gray-700",
              isRTL && "flex-row-reverse"
            )}>
              <group.icon className="w-5 h-5 text-zenith-sage-600 dark:text-zenith-sage-400" />
              <h2 className="text-xl font-semibold">{group.title}</h2>
            </div>

            <div className="space-y-4">
              {group.settings.map((setting, settingIndex) => (
                <div
                  key={settingIndex}
                  className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl p-6 hover:shadow-sm transition-all duration-200"
                >
                  <div className={cn(
                    "flex items-center justify-between",
                    isRTL && "flex-row-reverse"
                  )}>
                    <div className={cn("flex-1", isRTL && "text-right")}>
                      <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                        {setting.label}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {setting.description}
                      </p>
                    </div>
                    <div className="ml-4">
                      {setting.component}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Footer */}
      <div className="pt-8 border-t border-gray-200 dark:border-gray-700">
        <div className={cn(
          "flex items-center justify-center gap-4 text-sm text-gray-600 dark:text-gray-400",
          isRTL && "flex-row-reverse"
        )}>
          <span>© 2024 Zenith Flow</span>
          <span>•</span>
          <button className="hover:text-gray-900 dark:hover:text-white transition-colors">
            {isRTL ? 'سياسة الخصوصية' : 'Privacy Policy'}
          </button>
          <span>•</span>
          <button className="hover:text-gray-900 dark:hover:text-white transition-colors">
            {isRTL ? 'شروط الاستخدام' : 'Terms of Service'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Settings;
